<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>库存总量和批次分布测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .data-section {
            margin-bottom: 30px;
        }
        .data-title {
            font-size: 18px;
            font-weight: bold;
            color: #165DFF;
            margin-bottom: 15px;
        }
        .data-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
            margin-right: 10px;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>库存总量和批次分布数据结构测试</h1>
        
        <div class="data-section">
            <div class="data-title">📊 库存总量数据 (summaryData.storeAgeList)</div>
            <pre id="inventoryData"></pre>
            
            <div class="data-title">处理后的库存图例数据</div>
            <div id="inventoryLegend"></div>
        </div>
        
        <div class="data-section">
            <div class="data-title">📈 批次分布数据 (batchData.batchList)</div>
            <pre id="batchData"></pre>
            
            <div class="data-title">处理后的批次图例数据</div>
            <div id="batchLegend"></div>
        </div>
    </div>

    <script>
        // 模拟数据
        const summaryData = {
            storeAgeList: [
                {
                    storeAgeType: 1,
                    storeAgeNum: 45,
                    storeAgePercent: 15.5
                },
                {
                    storeAgeType: 2,
                    storeAgeNum: 128,
                    storeAgePercent: 44.2
                },
                {
                    storeAgeType: 3,
                    storeAgeNum: 117,
                    storeAgePercent: 40.3
                }
            ]
        };

        const batchData = {
            batchList: [
                {
                    batchType: 1,
                    batchNum: 89,
                    batchPercent: 30.7,
                    monthDiff: null
                },
                {
                    batchType: 2,
                    batchNum: 156,
                    batchPercent: 53.8,
                    monthDiff: null
                },
                {
                    batchType: 3,
                    batchNum: 34,
                    batchPercent: 11.7,
                    monthDiff: null
                },
                {
                    batchType: 4,
                    batchNum: 11,
                    batchPercent: 3.8,
                    monthDiff: null
                }
            ]
        };

        // 配置
        const inventoryConfig = [
            { type: 1, label: '低库龄预警', color: '#165DFF', unit: '条' },
            { type: 2, label: '中库龄库存', color: '#00B42A', unit: '条' },
            { type: 3, label: '高库龄库存', color: '#FF7D00', unit: '条' }
        ];

        const batchConfig = [
            { type: 1, label: '近1个月入库', color: '#165DFF' },
            { type: 2, label: '近1-3个月入库', color: '#00B42A' },
            { type: 3, label: '近3-6个月入库', color: '#FF7D00' },
            { type: 4, label: '近6个月以上', color: '#F53F3F' }
        ];

        // 处理数据的函数
        function processInventoryData() {
            const storeAgeList = summaryData.storeAgeList || [];
            return inventoryConfig.map(config => {
                const item = storeAgeList.find(store => store.storeAgeType === config.type) || {};
                return {
                    color: config.color,
                    label: config.label,
                    value: item.storeAgeNum || 0,
                    unit: config.unit
                };
            });
        }

        function processBatchData() {
            const batchList = batchData.batchList || [];
            return batchConfig.map(config => {
                const item = batchList.find(batch => batch.batchType === config.type) || {};
                return {
                    color: config.color,
                    label: config.label,
                    value: Math.round(item.batchPercent || 0)
                };
            });
        }

        // 渲染数据
        function renderData() {
            // 显示原始数据
            document.getElementById('inventoryData').textContent = JSON.stringify(summaryData, null, 2);
            document.getElementById('batchData').textContent = JSON.stringify(batchData, null, 2);

            // 显示处理后的数据
            const inventoryLegendData = processInventoryData();
            const batchLegendData = processBatchData();

            const inventoryLegendHtml = inventoryLegendData.map(item => `
                <div class="legend-item">
                    <span class="legend-color" style="background: ${item.color}"></span>
                    <span>${item.label}: ${item.value} ${item.unit}</span>
                </div>
            `).join('');

            const batchLegendHtml = batchLegendData.map(item => `
                <div class="legend-item">
                    <span class="legend-color" style="background: ${item.color}"></span>
                    <span>${item.label}: ${item.value}%</span>
                </div>
            `).join('');

            document.getElementById('inventoryLegend').innerHTML = inventoryLegendHtml;
            document.getElementById('batchLegend').innerHTML = batchLegendHtml;
        }

        // 页面加载完成后渲染数据
        document.addEventListener('DOMContentLoaded', renderData);
    </script>
</body>
</html>

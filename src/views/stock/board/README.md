# 库存看板接口对接说明

## 概述
本文档说明了库存看板页面的接口对接实现，包括库存总量、批次分布和库存筛选功能。

## 接口说明

### 1. 库存总量接口 (selectTotalNum)
- **接口路径**: `/mesapp-service/inventory/view/selectTotalNum`
- **请求方法**: POST
- **入参**:
```javascript
{
  "warehouseId": null, // 仓库ID，可为null
  "startTime": "2022-01-01", // 开始日期
  "endTime": null // 结束日期，可为null
}
```
- **返回数据结构**:
```javascript
{
  "result": "SUCCESS",
  "code": 200,
  "msg": "成功",
  "data": {
    "inCnt": 36, // 入库总数
    "inWeightNum": "580001", // 入库总重量
    "storeAgeList": [
      {
        "storeAgeType": 1, // 库龄类型：1-近期, 2-中期, 3-长期
        "storeAgeNum": 0, // 数量
        "storeAgePercent": 0 // 百分比
      }
    ]
  },
  "success": true
}
```

### 2. 批次分布接口 (selectInventoryList)
- **接口路径**: `/mesapp-service/inventory/view/selectBatchNum`
- **请求方法**: POST
- **入参**: 同库存总量接口
- **返回数据结构**:
```javascript
{
  "success": true,
  "message": "",
  "code": 200,
  "result": {
    "batchList": [
      {
        "batchType": 1, // 批次类型：1-近1个月, 2-近1-3个月, 3-近3-6个月, 4-近6个月以上
        "batchNum": 0, // 数量
        "batchPercent": 0 // 百分比
      }
    ]
  }
}
```

### 3. 库存筛选接口 (groupByProduct)
- **接口路径**: `/mesapp-service/warehouse/groupByProduct`
- **请求方法**: POST
- **入参**:
```javascript
{
  "pageNum": 1, // 页码
  "pageSize": 999 // 每页数量
}
```
- **返回数据结构**:
```javascript
{
  "success": true,
  "message": "成功",
  "code": 200,
  "result": {
    "total": "2",
    "list": [
      {
        "warehouseDetailId": "123123123123",
        "warehouseId": "1646065224472313858",
        "productId": "1646048520631468033",
        "productCode": "CP20230412151204079",
        "productName": "香辣牛肉条",
        "productTypeId": "1646038260357251073",
        "productTypeName": "牛肉",
        "specification": "1.00-5.00/kg",
        "inventoryNum": 8, // 库存数量
        "inventoryWeight": "521.00" // 库存重量
      }
    ]
  }
}
```

## 实现特点

### 1. 代码简洁优雅
- 使用async/await处理异步请求
- 统一的错误处理机制
- 数据格式转换逻辑清晰

### 2. 减少冗余代码
- 复用格式化函数（formatWeight）
- 统一的接口调用模式
- 组件化设计，职责分离

### 3. 全局导入
- 在页面顶部统一导入所需的API函数
- 按需导入，避免不必要的依赖

## 使用方法

### 1. 筛选查询
用户可以通过仓库选择和时间范围筛选来查询不同条件下的库存数据：
- 选择仓库：影响所有接口的warehouseId参数
- 选择时间范围：影响startTime和endTime参数
- 点击查询按钮：刷新所有数据

### 2. 数据展示
- **库存总量卡片**：显示入库总数、总重量、低库存预警、呆滞库存
- **批次分布图表**：以饼图形式展示不同时间段的库存分布
- **库存明细表格**：显示具体的产品库存信息，支持分页

### 3. 响应式处理
- 自动适配不同屏幕尺寸
- 表格高度自适应
- 移动端友好的交互设计

## 注意事项

1. **错误处理**：所有接口调用都包含try-catch错误处理
2. **默认值**：当接口返回空数据时，使用合理的默认值
3. **数据转换**：根据UI需求对接口数据进行适当转换
4. **性能优化**：使用Promise.all并行请求多个接口
5. **用户体验**：添加loading状态和错误提示

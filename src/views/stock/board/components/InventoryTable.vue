<template>
  <div class="inventory-table">
    <div class="table-header">
      <div class="title-section">
        <span class="title">库存总量</span>
      </div>
      <div class="action-buttons">
        <el-button type="primary" size="mini" icon="el-icon-search">查询</el-button>
        <el-button size="mini" icon="el-icon-refresh">重置</el-button>
      </div>
    </div>
    
    <!-- 查询条件 -->
    <div class="search-form">
      <el-form :model="searchParams" :inline="true" size="small" class="form_box">
        <el-form-item label="产品编码">
          <el-input v-model="searchParams.productCode" placeholder="请输入产品编码" style="width: 200px;"></el-input>
        </el-form-item>
        <el-form-item label="产品名称">
          <el-input v-model="searchParams.productName" placeholder="请输入产品名称" style="width: 200px;"></el-input>
        </el-form-item>
        <el-form-item label="所在仓库">
          <el-select v-model="searchParams.warehouseId" placeholder="请选择" style="width: 200px;">
            <el-option label="全部" value=""></el-option>
            <el-option
              v-for="item in warehouseList"
              :key="item.warehouseId"
              :label="item.warehouseName"
              :value="item.warehouseId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="库存状态">
          <el-select v-model="searchParams.status" placeholder="请选择" style="width: 200px;">
            <el-option label="全部" value=""></el-option>
            <el-option label="有库存" value="1"></el-option>
            <el-option label="无库存" value="2"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 表格 -->
    <div :style="{height: tableHeight + 'px'}">
      <el-table 
        :data="tableData" 
        stripe 
        style="width: 100%" 
        border 
        v-loading="loading" 
        :max-height="tableHeight"
      >
        <el-table-column type="index" width="55" align="center" label="序号"></el-table-column>
        <el-table-column prop="productCode" align="center" label="产品编码" show-overflow-tooltip></el-table-column>
        <el-table-column prop="productName" align="center" label="产品名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="productTypeName" align="center" label="产品类型" show-overflow-tooltip></el-table-column>
        <el-table-column prop="warehouseName" align="center" label="所在仓库" show-overflow-tooltip></el-table-column>
        <el-table-column prop="specification" align="center" label="规格" show-overflow-tooltip></el-table-column>
        <el-table-column prop="inventoryNum" align="center" label="库存数量" show-overflow-tooltip></el-table-column>
        <el-table-column prop="inventoryWeight" align="center" label="库存重量 (kg)" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ formatWeight(scope.row.inventoryWeight) }}
          </template>
        </el-table-column>
        <el-table-column prop="stockLevel" align="center" label="库存" show-overflow-tooltip>
          <template slot-scope="scope">
            <span :class="getStockLevelClass(scope.row.stockLevel)">
              {{ scope.row.stockLevel }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" align="center" label="状态" show-overflow-tooltip>
          <template slot-scope="scope">
            <span :class="getStatusClass(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleDetail(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 分页 -->
    <div class="pagination-box">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InventoryTable',
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    pagination: {
      type: Object,
      default: () => ({
        pageNum: 1,
        pageSize: 10,
        total: 0
      })
    },
    warehouseList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableHeight: 400,
      searchParams: {
        productCode: '',
        productName: '',
        warehouseId: '',
        status: ''
      }
    };
  },
  mounted() {
    this.setTableHeight();
    window.addEventListener('resize', this.setTableHeight);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.setTableHeight);
  },
  methods: {
    setTableHeight() {
      this.$nextTick(() => {
        const windowHeight = window.innerHeight;
        this.tableHeight = windowHeight - 500; // 根据实际情况调整
        if (this.tableHeight < 300) {
          this.tableHeight = 300;
        }
      });
    },
    
    handleSizeChange(val) {
      this.$emit('onPageSizeChange', val);
    },
    
    handleCurrentChange(val) {
      this.$emit('onPageChange', val);
    },
    
    handleDetail(row) {
      this.$emit('onDetail', row);
    },
    
    getStockLevelClass(stockLevel) {
      const level = parseInt(stockLevel);
      if (level > 80) return 'stock-high';
      if (level >= 50) return 'stock-medium';
      return 'stock-low';
    },
    
    getStatusClass(status) {
      switch (status) {
        case '1':
          return 'status-normal';
        case '2':
          return 'status-warning';
        default:
          return '';
      }
    },
    
    getStatusText(status) {
      switch (status) {
        case '1':
          return '有库存';
        case '2':
          return '无库存';
        default:
          return status;
      }
    },

    // 格式化重量显示
    formatWeight(weight) {
      if (!weight) return '0.00';
      const num = parseFloat(weight);
      if (num >= 1000) {
        return (num / 1000).toFixed(2) + 'T';
      }
      return num.toFixed(2);
    }
  }
};
</script>

<style lang="scss" scoped>
.inventory-table {
  background: white;
  border-radius: 8px;
  padding: 20px;
  
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .title {
      font-size: 16px;
      font-weight: 500;
      color: #165DFF;
    }
    
    .action-buttons {
      display: flex;
      gap: 10px;
    }
  }
  
  .search-form {
    margin-bottom: 20px;
    padding: 16px;
    background: #F7F8FA;
    border-radius: 6px;
  }
  
  .pagination-box {
    margin-top: 20px;
    text-align: right;
  }
  
  // 状态样式
  .stock-high {
    color: #F53F3F;
    font-weight: 500;
  }
  
  .stock-medium {
    color: #FF7D00;
    font-weight: 500;
  }
  
  .stock-low {
    color: #00B42A;
    font-weight: 500;
  }
  
  .status-normal {
    color: #00B42A;
    font-weight: 500;
  }
  
  .status-warning {
    color: #FF7D00;
    font-weight: 500;
  }
}
</style>

<template>
  <div class="warn-card">
    <!-- 低库存预警卡片 -->
    <el-card v-if="type === 'lowStock'" shadow="always" class="warning-card idle-material-card">
      <div class="card-content">
        <div class="card-header">
          <div class="warning-code">{{ data.code }}
            <span class="warning-date">
              {{ data.createTime }}
            </span>
          </div>
          <div class="warning-level-badge" :class="getLevelClass(data.level)">{{ data.levelText }}</div>
        </div>

        <div class="stock-section">
          <div class="stock-row">
            <div class="stock-item">
              <span class="stock-label">当前库存：</span>
              <span class="stock-value">{{ data.currentStock }}</span>
            </div>
            <div class="stock-item">
              <span class="stock-label">安全库存：</span>
              <span class="stock-value">{{ data.safeStock }}</span>
            </div>
            <div class="stock-item">
              <span class="stock-label" :class="data.currentStock < data.safeStock ? 'negative' : 'positive'">
                短缺量：
              </span>
              <span class="stock-diff stock-value">{{ data.warehouseInventorySubNum }}</span> 
            </div>
          </div>

          <div class="progress-container">
            <el-progress :percentage="getStockPercentage(data)" :color="getProgressColor(data)" :show-text="false"
              :stroke-width="8"></el-progress>
          </div>
        </div>

        <div class="card-actions">
          <div>
            <el-tag :type="getActionStatus(data.warningStatus).type">
              {{ getActionStatus(data.warningStatus).text }}
            </el-tag>

          </div>
          <div class="action-buttons">
            <el-button v-for="action in data.actions" :key="action" size="mini"
              :type="action === '处理' ? 'primary' : 'default'" @click="handleAction(action, data)">
              {{ action }}
            </el-button>
          </div>
        </div>
      </div>
    </el-card>
    <!-- 呆滞料预警 -->
    <el-card v-else-if="type === 'idleMat'" shadow="always" class="warning-card idle-material-card">
      <div class="card-content">
        <div class="card-header">
          <div class="warning-code">{{ data.code }}
            <span class="warning-date">{{ data.createTime }}</span>
          </div>
          <div class="warning-level-badge" :class="getLevelClass(data.level)">{{ data.levelText }}</div>
        </div>

        <div class="idle-info-section">
          <div class="info-row">
            <div class="info-item">
              <span class="info-label">最后使用：</span>
              <span class="info-value">{{ data.inWarehouseDate }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">闲置天数：</span>
              <span class="info-value idle-days">{{ data.idleDays }}</span>
              <span class="warning-level-tag" :class="getLevelClass(data.level)">{{ data.levelText }}</span>
            </div>
          </div>
        </div>

        <div class="card-actions">
          <div>
            <el-tag :type="getActionStatus(data.warningStatus).type">
              {{ getActionStatus(data.warningStatus).text }}
            </el-tag>
          </div>
          <div class="action-buttons">
            <el-button v-for="action in data.actions" :key="action" size="mini"
              :type="action === '处理' ? 'primary' : 'default'" @click="handleAction(action, data)">
              {{ action }}
            </el-button>
          </div>
        </div>
      </div>
    </el-card>
    <!-- 保质期预警卡片 -->
    <el-card v-else-if="type === 'expiry'" shadow="always" class="warning-card expiry-card">
      <div class="card-content">
        <div class="card-header">
          <div class="warning-code">{{ data.code }}（批次: {{ data.batch }}）
            <span class="warehouse-info">仓库名称：{{ data.warehouseName }}</span>
          </div>
          <div class="warning-date">{{ data.createTime }}</div>
          <div class="warning-level-badge" :class="getLevelClass(data.level)">{{ data.levelText }}</div>
        </div>

        <div class="expiry-info-section">
          <div class="info-row">
            <div class="info-item">
              <span class="info-label">保质期：</span>
              <span class="info-value">{{ data.expiryDate }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">剩余天数：</span>
              <span class="info-value remaining-days" :class="data.remainingDays < 0 ? 'expired' : 'warning'">
                {{ data.remainingDays }}
              </span>
            </div>
            <div class="info-item">
              <span class="info-label">建议处理：</span>
              <span class="info-value suggestion">{{ data.suggestionText }}</span>
            </div>
          </div>
        </div>

        <div class="card-actions">
          <div>
            <el-tag :type="getActionStatus(data.warningStatus).type">
              {{ getActionStatus(data.warningStatus).text }}
            </el-tag>
          </div>
          <div class="action-buttons">
            <el-button v-for="action in data.actions" :key="action" size="mini"
              :type="action === '处理' ? 'primary' : 'default'" @click="handleAction(action, data)">
              {{ action }}
            </el-button>
          </div>
        </div>
      </div>
    </el-card>



  </div>
</template>

<script>
import { updateWarningStatus, updateIgnoreStatus } from '@/api/warning.js'

export default {
  name: 'WarnCard',
  props: {
    data: {
      type: Object,
      required: true
    },
    type: {
      type: String,
      required: true
    }
  },
  methods: {
    // 获取等级样式类
    getLevelClass(level) {
      const classMap = {
        high: 'level-high',
        medium: 'level-medium',
        low: 'level-low'
      }
      return classMap[level] || 'level-low'
    },

    // 获取库存百分比
    getStockPercentage(data) {
      console.log('库存数量🚀🚀', data)
      if (!data.safeStock) return 0
      return Math.min((data.currentStock / data.safeStock) * 100, 100)
    },

    // 获取进度条颜色
    getProgressColor(data) {
      const percentage = this.getStockPercentage(data)
      if (percentage < 30) return '#F56C6C'
      if (percentage < 60) return '#E6A23C'
      return '#67C23A'
    },
    getActionStatus(status) {
      const statusMap = {
        1: { type: 'danger', text: '待处理' },
        2: { type: 'warning', text: '处理中' },
        3: { type: 'success', text: '已处理' }
      }
      return statusMap[status] || { type: 'info', text: '未知状态' }
    },

    // 处理操作按钮点击
    async handleAction(action, data) {
      try {
        const actionMap = {
          '处理': () => this.handleProcess(data),
          '忽略': () => this.handleIgnore(data)
        }

        const actionHandler = actionMap[action]
        if (actionHandler) {
          await actionHandler()
        }
      } catch (error) {
        console.error(`${action}操作失败:`, error)
        this.$message.error(`${action}操作失败，请重试`)
      }
    },

    // 通用的API调用处理方法
    async handleApiCall(apiFunction, params, successMessage, errorMessage) {
      const res = await apiFunction(params)
      if (res.success) {
        this.$message.success(successMessage)
        this.$emit('refresh')
      } else {
        this.$message.error(res.message || errorMessage)
      }
    },

    // 处理预警
    async handleProcess(data) {
      const params = {
        warningRecordId: data.id,
        warningStatus: 3 // 已处理
      }
      await this.handleApiCall(updateWarningStatus, params, '操作成功', '操作失败')
    },

    // 忽略预警
    async handleIgnore(data) {
      const params = { warningRecordId: data.id }
      await this.handleApiCall(updateIgnoreStatus, params, '操作成功', '操作失败')
    }
  }
}
</script>

<style scoped lang='scss'>
@import '@/assets/css/theme.scss';

.warn-card {
  margin-top: 10px;

  .warning-card {
    border: 1px solid #EEEEEE;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border-color: #D9D9D9;
    }

    ::v-deep .el-card__body {
      padding: 20px;
    }

    &.idle-material-card {
      .card-content {
        display: block;
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .warning-code {
          font-size: 16px;
          font-weight: 500;
          color: $title_text_color;
          flex: 1;

          .warning-date {
            display: block;
            font-size: 14px;
            color: $value_color;
            font-weight: normal;
            margin-top: 4px;
          }
        }

        .warning-date {
          font-size: 14px;
          color: $value_color;
        }

        .warning-level-badge {
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;

          &.level-high {
            background: #FF4D4F;
            color: white;
          }

          &.level-medium {
            background: #FFC700;
            color: #333;
          }

          &.level-low {
            background: #52C41A;
            color: white;
          }
        }
      }

      .idle-info-section {
        margin-bottom: 16px;

        .info-row {
          display: flex;
          align-items: center;
          gap: 24px;

          .info-item {
            display: flex;
            align-items: center;
            gap: 4px;

            .info-label {
              font-size: 14px;
              color: $label_color;
            }

            .info-value {
              font-size: 14px;
              color: $value_color;

              &.idle-days {
                font-weight: 500;
                color: #FF4D4F;
              }
            }

            .warning-level-tag {
              padding: 2px 6px;
              border-radius: 4px;
              font-size: 12px;
              margin-left: 8px;

              &.level-high {
                background: #FEF0F0;
                color: #FF4D4F;
              }

              &.level-medium {
                background: #FDF6EC;
                color: #FFC700;
              }

              &.level-low {
                background: #F0F9FF;
                color: #1890FF;
              }
            }
          }
        }
      }

      .stock-section {
        margin-bottom: 16px;

        .stock-row {
          display: flex;
          align-items: center;
          gap: 16px;
          margin-bottom: 12px;

          .stock-label {
            font-size: 14px;
            color: $label_color;
          }

          .stock-value {
            font-size: 16px;
            color: $value_color;
            font-weight: 500;
          }

          .stock-diff {
            color: #FF4D4F !important;

            &.negative {
              color: #FF4D4F;
            }

            &.positive {
              color: #52C41A;
            }
          }

          .stock-item {
            width: 30%;

            .stock-label {
              font-size: 14px;
              color: $label_color;
            }

            .stock-value {
              font-size: 16px;
              color: $value_color;
              margin-left: 4px;
            }
          }
        }

        .progress-container {
          ::v-deep .el-progress-bar {
            padding-right: 0;
          }
        }
      }

      .card-actions {
        display: flex;
        justify-content: space-between;

        .action-buttons {
          display: flex;
          gap: 8px;

          ::v-deep .el-button--primary {
            background: $theme_color;
            border-color: $theme_color;

            &:hover {
              background: lighten($theme_color, 10%);
              border-color: lighten($theme_color, 10%);
            }
          }
        }
      }
    }

    &.expiry-card {
      .card-header {
        align-items: flex-start;

        .warning-code {
          .warehouse-info {
            display: block;
            font-size: 14px;
            color: $value_color;
            font-weight: normal;
            margin-top: 4px;
          }
        }
      }

      .expiry-info-section {
        margin-bottom: 16px;

        .info-row {
          display: flex;
          align-items: center;
          gap: 24px;
          flex-wrap: wrap;

          .info-item {
            display: flex;
            align-items: center;
            gap: 4px;

            .info-label {
              font-size: 14px;
              color: $label_color;
            }

            .info-value {
              font-size: 14px;
              color: $value_color;

              &.remaining-days {
                font-weight: 500;

                &.expired {
                  color: #FF4D4F;
                }

                &.warning {
                  color: #FFC700;
                }
              }

              &.suggestion {
                font-weight: 500;
                color: $theme_color;
              }
            }
          }
        }
      }
    }
  }

  .card-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    min-height: 80px;
  }

  .card-left {
    flex: 1;
  }

  .card-right {
    margin-left: 20px;
    display: flex;
    align-items: center;
    height: 100%;
  }

  .warning-info {
    .warning-code {
      font-size: 16px;
      font-weight: 500;
      color: $title_text_color;
      margin-bottom: 8px;
    }

    .warning-header {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 8px;

      .batch-info {
        font-size: 14px;
        color: $value_color;
      }
    }

    .warning-meta {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 12px;

      .warning-time {
        font-size: 14px;
        color: $value_color;
      }

      .warning-level {
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;

        &.level-high {
          background: #FEF0F0;
          color: $red;
        }

        &.level-medium {
          background: #FDF6EC;
          color: $orange;
        }

        &.level-low {
          background: #F0F9FF;
          color: $blue;
        }
      }
    }

    .warning-detail {
      .detail-label {
        font-size: 14px;
        color: $label_color;
      }

      .detail-value {
        font-size: 14px;
        color: $value_color;
        margin-left: 4px;
      }

      .status-tag {
        margin-left: 12px;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;

        &.status-expired {
          background: #FEF0F0;
          color: $red;
        }

        &.status-expiring {
          background: #FDF6EC;
          color: $orange;
        }
      }



      .progress-container {
        margin-top: 8px;

        ::v-deep .el-progress-bar {
          padding-right: 0;
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .warn-card {
    .card-content {
      flex-direction: column;
      align-items: stretch;
    }

    .card-right {
      margin-left: 0;
      margin-top: 16px;
      justify-content: flex-end;
    }

    .warning-info {
      .warning-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }

      .warning-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }


    }
  }
}
</style>
